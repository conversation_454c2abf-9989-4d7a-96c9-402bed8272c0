"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Loader2, Activity } from 'lucide-react'
import { toast } from "@/components/ui/use-toast"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { Alert, AlertDescription } from "@/components/ui/alert"

interface EarthquakeData {
  pga: number
  probability: number
}

interface EarthquakeResponse {
  data: Array<{
    pga_value: number
    probability_of_exceedance: number
  }>
  metadata?: {
    target_latitude: number
    target_longitude: number
    window: number
    vs30?: number
  }
}

export function EarthquakePredictionTool() {
  const [formData, setFormData] = useState({
    latitude: '',
    longitude: '',
    window: '1',
    vs30: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [chartData, setChartData] = useState<EarthquakeData[]>([])
  const [error, setError] = useState<string | null>(null)
  const [hasResults, setHasResults] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    setError(null)
  }

  const handleWindowChange = (value: string) => {
    setFormData(prev => ({ ...prev, window: value }))
  }

  const validateInputs = () => {
    const lat = parseFloat(formData.latitude)
    const lng = parseFloat(formData.longitude)

    if (!formData.latitude || !formData.longitude) {
      return "Latitude and longitude are required"
    }

    if (isNaN(lat) || lat < -90 || lat > 90) {
      return "Latitude must be between -90 and 90 degrees"
    }

    if (isNaN(lng) || lng < -180 || lng > 180) {
      return "Longitude must be between -180 and 180 degrees"
    }

    if (formData.vs30 && (isNaN(parseFloat(formData.vs30)) || parseFloat(formData.vs30) <= 0)) {
      return "Vs30 must be a positive number"
    }

    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const validationError = validateInputs()
    if (validationError) {
      setError(validationError)
      return
    }

    setIsLoading(true)
    setError(null)
    setChartData([])
    setHasResults(false)

    try {
      const params = new URLSearchParams({
        target_latitude: formData.latitude,
        target_longitude: formData.longitude,
        window: formData.window
      })

      if (formData.vs30) {
        params.append('Vs30', formData.vs30)
      }

      const response = await fetch(`https://pga-curve-715610578807.us-central1.run.app/?${params}`)

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      // Transform the API response to chart data format
      let transformedData: EarthquakeData[] = []

      if (typeof data === 'object' && data !== null && !Array.isArray(data)) {
        // API returns an object with PGA values as keys and probabilities as values
        transformedData = Object.entries(data).map(([pgaStr, probability]) => ({
          pga: parseFloat(pgaStr),
          probability: (probability as number) * 100 // Convert to percentage
        }))
      } else if (Array.isArray(data)) {
        // If data is directly an array
        transformedData = data.map((item: any) => ({
          pga: item.pga_value || item.pga || item.x,
          probability: (item.probability_of_exceedance || item.probability || item.y) * 100
        }))
      } else if (data.data && Array.isArray(data.data)) {
        // If data is wrapped in a data property
        transformedData = data.data.map((item: any) => ({
          pga: item.pga_value || item.pga || item.x,
          probability: (item.probability_of_exceedance || item.probability || item.y) * 100
        }))
      }

      if (transformedData.length === 0) {
        throw new Error("No valid data received from API")
      }

      // Sort by PGA value for better chart visualization
      transformedData.sort((a, b) => a.pga - b.pga)

      setChartData(transformedData)
      setHasResults(true)

      toast({
        title: "Success",
        description: `Generated earthquake prediction curve with ${transformedData.length} data points`,
      })

    } catch (err) {
      console.error('API Error:', err)
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch earthquake prediction data"
      setError(errorMessage)

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleReset = () => {
    setFormData({
      latitude: '',
      longitude: '',
      window: '1',
      vs30: ''
    })
    setChartData([])
    setError(null)
    setHasResults(false)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Earthquake Prediction Analysis
          </CardTitle>
          <CardDescription>
            Generate probabilistic earthquake curves based on location coordinates and time window
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="latitude">Latitude *</Label>
                <Input
                  id="latitude"
                  name="latitude"
                  type="number"
                  step="any"
                  min="-90"
                  max="90"
                  value={formData.latitude}
                  onChange={handleInputChange}
                  placeholder="e.g., -15.02"
                  required
                />
                <p className="text-xs text-muted-foreground">Range: -90 to 90 degrees</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="longitude">Longitude *</Label>
                <Input
                  id="longitude"
                  name="longitude"
                  type="number"
                  step="any"
                  min="-180"
                  max="180"
                  value={formData.longitude}
                  onChange={handleInputChange}
                  placeholder="e.g., -72.15"
                  required
                />
                <p className="text-xs text-muted-foreground">Range: -180 to 180 degrees</p>
              </div>
            </div>

            <div className="space-y-3">
              <Label>Prediction Window *</Label>
              <RadioGroup value={formData.window} onValueChange={handleWindowChange}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="1" id="window-1" />
                  <Label htmlFor="window-1">1 Year</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="3" id="window-3" />
                  <Label htmlFor="window-3">3 Years</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="vs30">Vs30 (Optional)</Label>
              <Input
                id="vs30"
                name="vs30"
                type="number"
                step="any"
                min="0"
                value={formData.vs30}
                onChange={handleInputChange}
                placeholder="e.g., 760"
              />
              <p className="text-xs text-muted-foreground">
                Leave empty to use default USGS values
              </p>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex gap-3">
              <Button type="submit" disabled={isLoading} className="flex-1">
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating Prediction...
                  </>
                ) : (
                  "Generate Prediction"
                )}
              </Button>
              <Button type="button" variant="outline" onClick={handleReset}>
                Reset
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {hasResults && chartData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Earthquake Prediction Curve</CardTitle>
            <CardDescription>
              Probability of exceedance vs Peak Ground Acceleration (PGA) for {formData.window} year{formData.window === '1' ? '' : 's'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-96 w-full">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="pga"
                    label={{ value: 'PGA (g)', position: 'insideBottom', offset: -5 }}
                    tickFormatter={(value) => Number(value).toFixed(0)}
                  />
                  <YAxis
                    label={{ value: 'Probability of Exceedance (%)', angle: -90, position: 'insideLeft' }}
                    tickFormatter={(value) => `${Number(value).toFixed(1)}%`}
                  />
                  <Tooltip
                    formatter={(value, name) => [
                      `${Number(value).toFixed(2)}${name === 'probability' ? '%' : 'g'}`,
                      name === 'probability' ? 'Probability of Exceedance' : 'PGA'
                    ]}
                    labelFormatter={(label) => `PGA: ${Number(label).toFixed(0)}g`}
                  />
                  <Line
                    type="monotone"
                    dataKey="probability"
                    stroke="#2563eb"
                    strokeWidth={2}
                    dot={{ fill: '#2563eb', strokeWidth: 2, r: 3 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 text-sm text-muted-foreground">
              <p>
                <strong>Location:</strong> {formData.latitude}°, {formData.longitude}° |
                <strong> Window:</strong> {formData.window} year{formData.window === '1' ? '' : 's'}
                {formData.vs30 && <span> | <strong>Vs30:</strong> {formData.vs30}</span>}
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
