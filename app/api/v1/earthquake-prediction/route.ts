import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Extract parameters from the request
    const target_latitude = searchParams.get('target_latitude')
    const target_longitude = searchParams.get('target_longitude')
    const window = searchParams.get('window')
    const vs30 = searchParams.get('Vs30')

    // Validate required parameters
    if (!target_latitude || !target_longitude || !window) {
      return NextResponse.json(
        { error: 'Missing required parameters: target_latitude, target_longitude, window' },
        { status: 400 }
      )
    }

    // Validate parameter ranges
    const lat = parseFloat(target_latitude)
    const lng = parseFloat(target_longitude)
    const windowNum = parseInt(window)

    if (isNaN(lat) || lat < -90 || lat > 90) {
      return NextResponse.json(
        { error: 'Invalid latitude: must be between -90 and 90' },
        { status: 400 }
      )
    }

    if (isNaN(lng) || lng < -180 || lng > 180) {
      return NextResponse.json(
        { error: 'Invalid longitude: must be between -180 and 180' },
        { status: 400 }
      )
    }

    if (windowNum !== 1 && windowNum !== 3) {
      return NextResponse.json(
        { error: 'Invalid window: must be 1 or 3' },
        { status: 400 }
      )
    }

    // Build the external API URL
    const params = new URLSearchParams({
      target_latitude: target_latitude,
      target_longitude: target_longitude,
      window: window
    })

    if (vs30) {
      params.append('Vs30', vs30)
    }

    const externalApiUrl = `https://pga-curve-715610578807.us-central1.run.app/?${params}`
    
    console.log('Proxying request to:', externalApiUrl)

    // Make the request to the external API
    const response = await fetch(externalApiUrl, {
      method: 'GET',
      headers: {
        'Accept': '*/*',
        'User-Agent': 'Rekover-Earthquake-Prediction/1.0',
      },
    })

    if (!response.ok) {
      console.error('External API error:', response.status, response.statusText)
      return NextResponse.json(
        { error: `External API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      )
    }

    // Get the response as text first
    const responseText = await response.text()
    
    // Try to parse as JSON
    let data
    try {
      data = JSON.parse(responseText)
    } catch (parseError) {
      console.error('JSON parse error:', parseError)
      console.error('Response text:', responseText.substring(0, 500))
      return NextResponse.json(
        { error: 'Invalid JSON response from external API' },
        { status: 502 }
      )
    }

    // Return the data with proper CORS headers
    return NextResponse.json(data, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    })

  } catch (error) {
    console.error('Proxy API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
